# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/

# 静态文件
# static/ 目录需要被跟踪，但可以忽略其中的某些文件
static/images/uploads/
static/files/uploads/

# IDE
.idea/
.vscode/
*.swp
*.swo

# 操作系统
.DS_Store
Thumbs.db

# Virtual Environment
venv/
env/
ENV/
.env

# Project specific
.env.local
.env.development.local
.env.test.local
.env.production.local

# Coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*/settings/local.py
media/
staticfiles/

secrets.env
